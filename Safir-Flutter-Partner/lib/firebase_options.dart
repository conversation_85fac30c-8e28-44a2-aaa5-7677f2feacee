// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCPF95M174qbLlfGH7y04aQwJlWZsW8Q8w',
    appId: '1:815201363151:web:de2adf9293b202be4ec3ba',
    messagingSenderId: '815201363151',
    projectId: 'shaklabx',
    authDomain: 'shaklabx.firebaseapp.com',
    databaseURL: 'https://shaklabx-default-rtdb.firebaseio.com',
    storageBucket: 'shaklabx.firebasestorage.app',
    measurementId: 'G-1532VXM8HR',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDuZ4M2wLoMYSDKkXJAWEMjH2tJWowT3C4',
    appId: '1:815201363151:android:c28f38c139fe62164ec3ba',
    messagingSenderId: '815201363151',
    projectId: 'shaklabx',
    databaseURL: 'https://shaklabx-default-rtdb.firebaseio.com',
    storageBucket: 'shaklabx.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBWlQpP2J7nlsNiqG-yFM12C1ZriTdoohk',
    appId: '1:815201363151:ios:68092e78ceda9e524ec3ba',
    messagingSenderId: '815201363151',
    projectId: 'shaklabx',
    databaseURL: 'https://shaklabx-default-rtdb.firebaseio.com',
    storageBucket: 'shaklabx.firebasestorage.app',
    iosBundleId: 'com.shaklabx.partner',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBWlQpP2J7nlsNiqG-yFM12C1ZriTdoohk',
    appId: '1:815201363151:ios:68092e78ceda9e524ec3ba',
    messagingSenderId: '815201363151',
    projectId: 'shaklabx',
    databaseURL: 'https://shaklabx-default-rtdb.firebaseio.com',
    storageBucket: 'shaklabx.firebasestorage.app',
    iosBundleId: 'com.shaklabx.partner',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCPF95M174qbLlfGH7y04aQwJlWZsW8Q8w',
    appId: '1:815201363151:web:fce5c4d6fdb4c5224ec3ba',
    messagingSenderId: '815201363151',
    projectId: 'shaklabx',
    authDomain: 'shaklabx.firebaseapp.com',
    databaseURL: 'https://shaklabx-default-rtdb.firebaseio.com',
    storageBucket: 'shaklabx.firebasestorage.app',
    measurementId: 'G-Y0R1VLJ2RW',
  );
}
