{"flutter": {"platforms": {"android": {"default": {"projectId": "shaklabx", "appId": "1:815201363151:android:c28f38c139fe62164ec3ba", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "shaklabx", "appId": "1:815201363151:ios:68092e78ceda9e524ec3ba", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "shaklabx", "appId": "1:815201363151:ios:68092e78ceda9e524ec3ba", "uploadDebugSymbols": false, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "shaklabx", "configurations": {"android": "1:815201363151:android:c28f38c139fe62164ec3ba", "ios": "1:815201363151:ios:68092e78ceda9e524ec3ba", "macos": "1:815201363151:ios:68092e78ceda9e524ec3ba", "web": "1:815201363151:web:de2adf9293b202be4ec3ba", "windows": "1:815201363151:web:fce5c4d6fdb4c5224ec3ba"}}}}}, "database": {"rules": "database.rules.json"}}