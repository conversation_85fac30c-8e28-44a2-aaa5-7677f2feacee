import 'package:flutter/material.dart';
import 'package:safir_client/Core/utils/app_assets.dart';
import 'package:safir_client/pages/NavigatorPages/paymentgateways.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translations/translation.dart';
import '../../widgets/widgets.dart';
import '../NavigatorPages/walletpage.dart';
import '../loadingPage/loading.dart';
import '../login/login_view.dart';
import 'booking_confirmation.dart';
import 'map_page.dart';
import 'review_page.dart';

class Invoice extends StatefulWidget {
  const Invoice({super.key});

  @override
  State<Invoice> createState() => _InvoiceState();
}

class _InvoiceState extends State<Invoice> {
  bool _choosePayment = false;
  bool _isLoading = false;
  bool _choosePaymentMethod = false;
  String _error = '';
  String myPaymentMethod = '';

  @override
  void initState() {
    myMarkers.clear();
    promoCode = '';
    payingVia = 0;
    timing = null;
    promoStatus = null;
    super.initState();
  }

  navigateLogout() {
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => const LoginView()),
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return SafeArea(
      child: Material(
        child: Directionality(
          textDirection: (languageDirection == 'rtl')
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: ValueListenableBuilder(
            valueListenable: valueNotifierHome.value,
            builder: (context, value, child) {
              return Stack(
                children: [
                  if (userRequestData.isNotEmpty)
                    Container(
                      padding: EdgeInsets.all(media.width * 0.05),
                      height: media.height * 1,
                      width: media.width * 1,
                      color: page,
                      child: Column(
                        children: [
                          Expanded(
                            child: SingleChildScrollView(
                              physics: const BouncingScrollPhysics(),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  invoiceAppBar(media),
                                  const SizedBox(height: 20),
                                  driverDetails(media),
                                  const SizedBox(height: 16),
                                  tripDetails(media),
                                  const SizedBox(height: 24),
                                  tripAddressesDetails(media),
                                  const SizedBox(height: 24),
                                  if (userRequestData['requestBill'] != null)
                                    fareDetails(media),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 4),
                          invoiceSummary(media),
                          if (userRequestData['is_paid'] == 0 &&
                              userRequestData['payment_opt'] == '0')
                            payButton(media),
                        ],
                      ),
                    ),
                  if (_choosePayment) choosePaymentCardMethod(media),
                  if (_choosePaymentMethod) choosePaymentMethodPopup(media),
                  if (_isLoading == true) const Positioned(child: Loading())
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget choosePaymentCardMethod(Size media) {
    return Positioned(
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: media.width * 0.9,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () => setState(() => _choosePayment = false),
                    child: Container(
                      height: media.width * 0.1,
                      width: media.width * 0.1,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: page,
                      ),
                      child: Icon(
                        Icons.cancel_outlined,
                        color: textColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            Container(
              padding: EdgeInsets.all(media.width * 0.05),
              width: media.width * 0.9,
              height: media.height * 0.5,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: page,
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        languages[choosenLanguage]['text_choose_payment'],
                        style: GoogleFonts.notoKufiArabic(
                          fontSize: media.width * eighteen,
                          fontWeight: FontWeight.w600,
                          color: textColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Column(
                        children: paymentGateways
                            .map(
                              (i, value) {
                                return MapEntry(
                                  i,
                                  InkWell(
                                    onTap: () async {
                                      addMoney = double.parse(
                                          userRequestData['requestBill']['data']
                                                  ['total_amount']
                                              .toStringAsFixed(2));
                                      var val = await Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => PaymentGateways(
                                            from: '1',
                                            url: paymentGateways[i]['url'],
                                          ),
                                        ),
                                      );

                                      if (val != null) {
                                        if (val) {
                                          setState(() {
                                            _isLoading = true;
                                            _choosePayment = false;
                                          });
                                          ismulitipleride = true;
                                          var val = await getUserDetails(
                                            id: userRequestData['id'],
                                          );
                                          if (val == 'logout') {
                                            navigateLogout();
                                          }
                                          setState(() => _isLoading = false);
                                        }
                                      }
                                    },
                                    child: Container(
                                      height: media.width * 0.15,
                                      width: media.width * 0.6,
                                      margin: const EdgeInsets.only(bottom: 8),
                                      decoration: BoxDecoration(
                                        image: DecorationImage(
                                          image: NetworkImage(
                                            paymentGateways[i]['image'],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            )
                            .values
                            .toList(),
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget tripAddressesDetails(Size media) {
    return Column(
      children: [
        Row(
          children: [
            Container(
              height: media.width * 0.05,
              width: media.width * 0.05,
              alignment: Alignment.center,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.green,
              ),
              child: Container(
                height: media.width * 0.025,
                width: media.width * 0.025,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: MyText(
                text: userRequestData['pick_address'],
                size: media.width * twelve,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (tripStops.isNotEmpty)
          Column(
            children: tripStops
                .asMap()
                .map(
                  (i, value) {
                    return MapEntry(
                      i,
                      (i < tripStops.length - 1)
                          ? Container(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: Row(
                                children: [
                                  Container(
                                    height: media.width * 0.05,
                                    width: media.width * 0.05,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.red.withOpacity(0.4),
                                    ),
                                    child: MyText(
                                      text: (i + 1).toString(),
                                      color: const Color(0xFFFF0000),
                                      fontweight: FontWeight.w600,
                                      size: media.width * twelve,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: MyText(
                                      text: tripStops[i]['address'],
                                      size: media.width * twelve,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : Container(),
                    );
                  },
                )
                .values
                .toList(),
          ),
        Row(
          children: [
            Container(
              height: media.width * 0.05,
              width: media.width * 0.05,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.red.withOpacity(0.4),
              ),
              child: Icon(
                Icons.location_on,
                size: media.width * 0.03,
                color: const Color(0xFFFF0000),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: MyText(
                text: userRequestData['drop_address'],
                size: media.width * twelve,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget choosePaymentMethodPopup(Size media) {
    return Positioned(
      top: 0,
      child: Container(
        height: media.height * 1,
        width: media.width * 1,
        color: Colors.transparent.withOpacity(0.6),
        child: SizedBox(
          height: media.height * 1,
          width: media.width * 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: media.width * 0.9,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    InkWell(
                      onTap: () => setState(() => _choosePaymentMethod = false),
                      child: Container(
                        height: media.width * 0.1,
                        width: media.width * 0.1,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: page,
                        ),
                        child: Icon(
                          Icons.cancel_outlined,
                          color: textColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              Container(
                width: media.width * 0.9,
                decoration: BoxDecoration(
                  color: page,
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: EdgeInsets.all(media.width * 0.05),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      languages[choosenLanguage]['text_paymentmethod'],
                      style: GoogleFonts.notoKufiArabic(
                        fontSize: media.width * twenty,
                        fontWeight: FontWeight.w600,
                        color: textColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Column(
                      children: userRequestData['payment_type']
                          .toString()
                          .split(',')
                          .toList()
                          .asMap()
                          .map(
                            (i, value) {
                              String paymentType =
                                  userRequestData['payment_type']
                                      .toString()
                                      .split(',')
                                      .toList()[i];
                              return MapEntry(
                                i,
                                (paymentType != 'wallet')
                                    ? InkWell(
                                        onTap: () => setState(() {
                                          payingVia = i;
                                          myPaymentMethod = paymentType;
                                        }),
                                        child: Container(
                                          padding: EdgeInsets.all(
                                            media.width * 0.02,
                                          ),
                                          width: media.width * 0.9,
                                          child: Row(
                                            children: [
                                              showPaymentImage(
                                                media,
                                                paymentType,
                                              ),
                                              const SizedBox(width: 16),
                                              paymentDetailsItem(
                                                media,
                                                paymentType,
                                              ),
                                              const Spacer(),
                                              customRadioButton(
                                                media,
                                                paymentType,
                                              ),
                                            ],
                                          ),
                                        ),
                                      )
                                    : Container(),
                              );
                            },
                          )
                          .values
                          .toList(),
                    ),
                    const SizedBox(height: 16),
                    if (_error != '')
                      Container(
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(bottom: media.height * 0.02),
                        child: Text(
                          _error,
                          style: GoogleFonts.notoSans(
                            fontSize: media.width * fourteen,
                            color: Colors.red,
                          ),
                          maxLines: 1,
                        ),
                      ),
                    Button(
                      color: buttonColor,
                      textcolor: buttonText,
                      onTap: () async {
                        if ((myPaymentMethod == 'cash' &&
                                userRequestData['payment_opt'] != '1') ||
                            (myPaymentMethod == 'card' &&
                                userRequestData['payment_opt'] != '0')) {
                          if (myPaymentMethod != '') {
                            setState(() {
                              _isLoading = true;
                              _error = '';
                            });
                            var val = await paymentMethod(myPaymentMethod);
                            if (val == 'logout') {
                              navigateLogout();
                            } else if (val == 'success') {
                              if (myPaymentMethod == 'card') {
                                if (walletBalance.isEmpty) {
                                  var val = await getWalletHistory();
                                  if (val == 'logout') {
                                    navigateLogout();
                                  }
                                }
                                setState(
                                  () {
                                    _isLoading = false;
                                    _choosePaymentMethod = false;
                                    showModalBottomSheet(
                                      context: context,
                                      isScrollControlled: true,
                                      builder: (context) {
                                        return Container(
                                          padding: EdgeInsets.all(
                                              media.width * 0.05),
                                          height: media.width * 1,
                                          width: media.width * 1,
                                          decoration: BoxDecoration(
                                            color: page,
                                            borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(
                                                  media.width * 0.05),
                                              topRight: Radius.circular(
                                                  media.width * 0.05),
                                            ),
                                          ),
                                          child: SingleChildScrollView(
                                            child: Column(
                                              children: paymentGateways
                                                  .map(
                                                    (i, value) {
                                                      return MapEntry(
                                                        i,
                                                        (paymentGateways[i][
                                                                    'enabled'] ==
                                                                true)
                                                            ? InkWell(
                                                                onTap:
                                                                    () async {
                                                                  Navigator.pop(
                                                                      context);
                                                                  var val =
                                                                      await Navigator
                                                                          .push(
                                                                    context,
                                                                    MaterialPageRoute(
                                                                      builder:
                                                                          (context) =>
                                                                              PaymentGateways(
                                                                        url: paymentGateways[i]
                                                                            [
                                                                            'url'],
                                                                      ),
                                                                    ),
                                                                  );
                                                                  if (val !=
                                                                      null) {
                                                                    if (val) {
                                                                      setState(
                                                                          () {
                                                                        _isLoading =
                                                                            true;
                                                                        _choosePayment =
                                                                            false;
                                                                      });
                                                                      ismulitipleride =
                                                                          true;
                                                                      var val =
                                                                          await getUserDetails(
                                                                              id: userRequestData['id']);
                                                                      if (val ==
                                                                          'logout') {
                                                                        navigateLogout();
                                                                      }
                                                                      setState(() =>
                                                                          _isLoading =
                                                                              false);
                                                                    }
                                                                  }
                                                                },
                                                                child:
                                                                    Container(
                                                                  height: media
                                                                          .width *
                                                                      0.12,
                                                                  width: media
                                                                          .width *
                                                                      0.6,
                                                                  margin: EdgeInsets.only(
                                                                      bottom: media
                                                                              .width *
                                                                          0.02),
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    image:
                                                                        DecorationImage(
                                                                      image:
                                                                          NetworkImage(
                                                                        paymentGateways[i]
                                                                            [
                                                                            'image'],
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              )
                                                            : Container(),
                                                      );
                                                    },
                                                  )
                                                  .values
                                                  .toList(),
                                            ),
                                          ),
                                        );
                                      },
                                    );
                                  },
                                );
                              } else {
                                _choosePaymentMethod = false;
                              }
                            } else {
                              _error = val.toString();
                            }

                            setState(() {
                              myPaymentMethod = '';
                              _isLoading = false;
                            });
                          }
                        } else {
                          setState(() => _choosePaymentMethod = false);
                        }
                      },
                      text: languages[choosenLanguage]['text_confirm'],
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget paymentDetailsItem(Size media, String paymentType) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          paymentType,
          style: GoogleFonts.cairo(
            fontSize: media.width * fourteen,
            fontWeight: FontWeight.w600,
            color: textColor,
          ),
        ),
        Text(
          (paymentType == 'cash')
              ? languages[choosenLanguage]['text_paycash']
              : (paymentType == 'wallet')
                  ? languages[choosenLanguage]['text_paywallet']
                  : (paymentType == 'card')
                      ? languages[choosenLanguage]['text_paycard']
                      : (paymentType == 'upi')
                          ? languages[choosenLanguage]['text_payupi']
                          : '',
          style: GoogleFonts.cairo(
            fontSize: media.width * ten,
            color: textColor,
          ),
        )
      ],
    );
  }

  Widget customRadioButton(Size media, String paymentType) {
    return Container(
      height: media.width * 0.05,
      width: media.width * 0.05,
      decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: page,
          border: Border.all(color: textColor, width: 1.2)),
      alignment: Alignment.center,
      child: (myPaymentMethod == paymentType)
          ? Container(
              height: media.width * 0.03,
              width: media.width * 0.03,
              decoration:
                  BoxDecoration(color: textColor, shape: BoxShape.circle))
          : Container(),
    );
  }

  Widget showPaymentImage(Size media, String paymentType) {
    return SizedBox(
      width: media.width * 0.06,
      child: (paymentType == 'cash')
          ? Image.asset(
              AppAssets.cash,
              fit: BoxFit.contain,
            )
          : (paymentType == 'wallet')
              ? Image.asset(
                  AppAssets.wallet,
                  fit: BoxFit.contain,
                )
              : (paymentType == 'card')
                  ? Image.asset(
                      AppAssets.card,
                      fit: BoxFit.contain,
                    )
                  : (paymentType == 'upi')
                      ? Image.asset(
                          AppAssets.upi,
                          fit: BoxFit.contain,
                        )
                      : Container(),
    );
  }

  Widget invoiceAppBar(Size media) {
    return MyText(
      text: languages[choosenLanguage]['text_tripsummary'],
      size: media.width * sixteen,
      fontweight: FontWeight.bold,
    );
  }

  Widget driverDetails(Size media) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          height: media.width * 0.13,
          width: media.width * 0.13,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            image: DecorationImage(
              image: NetworkImage(
                userRequestData['driverDetail']['data']['profile_picture'],
              ),
              fit: BoxFit.cover,
            ),
          ),
        ),
        const SizedBox(width: 16),
        MyText(
          text: userRequestData['driverDetail']['data']['name'],
          size: media.width * eighteen,
        )
      ],
    );
  }

  Widget tripDetails(Size media) {
    return Container(
      padding: EdgeInsets.all(media.width * 0.04),
      decoration: BoxDecoration(color: Colors.grey.withOpacity(0.1)),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Column(
                children: [
                  MyText(
                    text: languages[choosenLanguage]['text_reference'],
                    size: media.width * fourteen,
                  ),
                  const SizedBox(height: 8),
                  MyText(
                    text: userRequestData['request_number'],
                    size: media.width * twelve,
                    fontweight: FontWeight.w700,
                  )
                ],
              ),
              Column(
                children: [
                  MyText(
                    text: languages[choosenLanguage]['text_rideType'],
                    size: media.width * fourteen,
                  ),
                  const SizedBox(height: 8),
                  MyText(
                    text: (userRequestData['is_rental'] == false)
                        ? languages[choosenLanguage]['text_regular']
                        : languages[choosenLanguage]['text_rental'],
                    size: media.width * twelve,
                    fontweight: FontWeight.w700,
                  )
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Column(
                children: [
                  MyText(
                    text: languages[choosenLanguage]['text_distance'],
                    size: media.width * fourteen,
                  ),
                  const SizedBox(height: 8),
                  MyText(
                    text: userRequestData['total_distance'] +
                        ' ' +
                        userRequestData['unit'],
                    size: media.width * twelve,
                    fontweight: FontWeight.w700,
                  )
                ],
              ),
              Column(
                children: [
                  MyText(
                    text: languages[choosenLanguage]['text_duration'],
                    size: media.width * fourteen,
                  ),
                  const SizedBox(height: 8),
                  MyText(
                    text:
                        '${userRequestData['total_time']} ${languages[choosenLanguage]['text_mins']}',
                    size: media.width * twelve,
                    fontweight: FontWeight.w700,
                  )
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget fareDetails(Size media) {
    return Column(
      children: [
        MyText(
          text: languages[choosenLanguage]['text_tripfare'],
          size: media.width * fourteen,
          fontweight: FontWeight.w700,
        ),
        const SizedBox(height: 16),
        if (userRequestData['is_rental'] == true)
          Container(
            padding: EdgeInsets.only(bottom: media.width * 0.05),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  languages[choosenLanguage]['text_ride_type'],
                  style: GoogleFonts.notoSans(
                      fontSize: media.width * fourteen, color: textColor),
                ),
                Text(
                  userRequestData['rental_package_name'],
                  style: GoogleFonts.notoSans(
                      fontSize: media.width * fourteen, color: textColor),
                ),
              ],
            ),
          ),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_baseprice'],
          (userRequestData.isNotEmpty && userRequestData['requestBill'] != null)
              ? userRequestData['requestBill']['data']['base_price'].toString()
              : '',
        ),
        fareDetailsDivider(media),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_distprice'],
          (userRequestData.isNotEmpty && userRequestData['requestBill'] != null)
              ? userRequestData['requestBill']['data']['distance_price']
                  .toString()
              : '',
        ),
        fareDetailsDivider(media),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_timeprice'],
          (userRequestData.isNotEmpty && userRequestData['requestBill'] != null)
              ? userRequestData['requestBill']['data']['time_price'].toString()
              : '',
        ),
        fareDetailsDivider(media),
        if (userRequestData['requestBill'] != null &&
            userRequestData['requestBill']['data']['cancellation_fee'] != 0)
          Column(
            children: [
              fareDetailsItem(
                media,
                languages[choosenLanguage]['text_cancelfee'],
                (userRequestData.isNotEmpty &&
                        userRequestData['requestBill'] != null)
                    ? userRequestData['requestBill']['data']['cancellation_fee']
                        .toString()
                    : '',
              ),
              fareDetailsDivider(media),
            ],
          ),
        if (userRequestData['requestBill'] != null &&
            userRequestData['requestBill']['data']['airport_surge_fee'] != 0)
          Column(
            children: [
              fareDetailsItem(
                media,
                languages[choosenLanguage]['text_surge_fee'],
                (userRequestData.isNotEmpty &&
                        userRequestData['requestBill'] != null)
                    ? userRequestData['requestBill']['data']
                            ['airport_surge_fee']
                        .toString()
                    : '',
              ),
              fareDetailsDivider(media),
            ],
          ),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_waiting_price'] +
              ' (' +
              userRequestData['requestBill']['data']
                  ['requested_currency_symbol'] +
              ' ' +
              userRequestData['requestBill']['data']['waiting_charge_per_min']
                  .toString() +
              ' x ' +
              userRequestData['requestBill']['data']['calculated_waiting_time']
                  .toString() +
              ' ' +
              languages[choosenLanguage]['text_mins'] +
              ')',
          userRequestData['requestBill']['data']['waiting_charge'].toString(),
        ),
        fareDetailsDivider(media),
        if (userRequestData['requestBill'] != null &&
            userRequestData['requestBill']['data']['admin_commision'] != 0)
          Column(
            children: [
              fareDetailsItem(
                media,
                languages[choosenLanguage]['text_convfee'],
                userRequestData['requestBill']['data']['admin_commision']
                    .toString(),
              ),
              fareDetailsDivider(media),
            ],
          ),
        if (userRequestData['requestBill'] != null &&
            userRequestData['requestBill']['data']['promo_discount'] != 0)
          Column(
            children: [
              fareDetailsItem(
                media,
                languages[choosenLanguage]['text_discount'],
                userRequestData['requestBill']['data']['promo_discount']
                    .toString(),
                true,
              ),
              fareDetailsDivider(media),
            ],
          ),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_taxes'],
          userRequestData['requestBill']['data']['service_tax'].toString(),
        ),
        fareDetailsDivider(media),
        fareDetailsItem(
          media,
          languages[choosenLanguage]['text_totalfare'],
          userRequestData['requestBill']['data']['total_amount'].toString(),
        ),
      ],
    );
  }

  Widget fareDetailsItem(Size media, String text, String amount,
      [bool isRed = false]) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        MyText(
          text: text,
          size: media.width * twelve,
          color: isRed ? Colors.red : null,
        ),
        MyText(
          text: userRequestData['requestBill']['data']
                  ['requested_currency_symbol'] +
              ' ' +
              amount,
          size: media.width * twelve,
          color: isRed ? Colors.red : null,
        ),
      ],
    );
  }

  Widget fareDetailsDivider(Size media) {
    return Container(
      margin:
          EdgeInsets.only(top: media.width * 0.03, bottom: media.width * 0.03),
      height: 1.5,
      color: const Color(0xffE0E0E0),
    );
  }

  Widget invoiceSummary(Size media) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          (userRequestData['payment_opt'] == '1')
              ? languages[choosenLanguage]['text_cash']
              : (userRequestData['payment_opt'] == '2')
                  ? languages[choosenLanguage]['text_wallet']
                  : languages[choosenLanguage]['text_card'],
          style: GoogleFonts.cairo(
            fontSize: media.width * sixteen,
            color: buttonColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: 2),
        MyText(
          text:
              ' ${userRequestData['requestBill']['data']['requested_currency_symbol']}',
          size: media.width * fourteen,
        ),
        MyText(
          text: ' ${userRequestData['requestBill']['data']['total_amount']}',
          size: media.width * twenty,
          fontweight: FontWeight.bold,
        ),
        const Spacer(),
        Button(
          color: buttonColor,
          textcolor: buttonText,
          onTap: () async {
            if (userRequestData['is_paid'] == 0 &&
                userRequestData['payment_opt'] != '2') {
              setState(() {
                myPaymentMethod = userRequestData['payment_opt'] == '0'
                    ? 'card'
                    : userRequestData['payment_opt'] == '1'
                        ? 'cash'
                        : '';
                _choosePaymentMethod = true;
              });
            } else {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const Review()),
              );
            }
          },
          text: (userRequestData['is_paid'] == 0 &&
                  userRequestData['payment_opt'] != '2')
              ? languages[choosenLanguage]['text_choose_payment']
              : languages[choosenLanguage]['text_confirm'],
        ),
      ],
    );
  }

  Widget payButton(Size media) {
    return Column(
      children: [
        const SizedBox(height: 8),
        Button(
          color: buttonColor,
          textcolor: buttonText,
          onTap: () async {
            setState(() => _isLoading = true);
            var val = await getWalletHistory();
            if (val == 'logout') {
              navigateLogout();
            }
            setState(() {
              _isLoading = false;
              _choosePayment = true;
            });
          },
          text: languages[choosenLanguage]['text_pay'],
        )
      ],
    );
  }
}
