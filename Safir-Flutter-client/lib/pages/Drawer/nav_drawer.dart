import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:safir_client/Core/utils/app_assets.dart';
import 'package:safir_client/Core/utils/custom_background.dart';
import 'package:safir_client/pages/Navigation%20Bar/manager/bottom_nav_bar_cubit/bottom_nav_bar_cubit.dart';
import 'package:safir_client/pages/NavigatorPages/makecomplaint.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:safir_client/pages/Orders/Presentation/Views/orders_view.dart';
import 'package:safir_client/pages/login/login_view.dart';
import '../../functions/functions.dart';
import '../../styles/styles.dart';
import '../../translations/translation.dart';
import '../../widgets/nav_menu.dart';
import '../../widgets/widgets.dart';
import '../NavigatorPages/favourite.dart';
import '../NavigatorPages/history.dart';
import '../NavigatorPages/notification.dart';
import '../NavigatorPages/referral.dart';
import '../NavigatorPages/sos.dart';
import '../NavigatorPages/support.dart';

class NavDrawer extends StatefulWidget {
  const NavDrawer({super.key});
  @override
  State<NavDrawer> createState() => _NavDrawerState();
}

class _NavDrawerState extends State<NavDrawer> {
  @override
  Widget build(BuildContext context) {
    var media = MediaQuery.of(context).size;
    return ValueListenableBuilder(
      valueListenable: valueNotifierHome.value,
      builder: (context, value, child) {
        return Container(
          decoration: BoxDecoration(
            image: customBackground,
            color: page,
          ),
          width: media.width * 1,
          child: Directionality(
            textDirection: (languageDirection == 'rtl')
                ? TextDirection.rtl
                : TextDirection.ltr,
            child: Drawer(
              shape: const RoundedRectangleBorder(),
              backgroundColor: Colors.transparent,
              child: SafeArea(
                child: Container(
                  padding: const EdgeInsets.only(bottom: 16),
                  width: media.width * 1,
                  child: Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const SizedBox(height: 16),
                              // navDrawerProfile(media),
                              SizedBox(
                                width: media.width * 0.9,
                                child: Column(
                                  children: [
                                    navDrawerHistory(media),
                                    navDrawerOrders(media),
                                    navDrawerNotification(media),
                                    navDrawerSOS(media),
                                    navDrawerMakeComplaints(media),
                                    navDrawerFavorite(media),
                                    navDrawerSupport(media),
                                    navDrawerReferral(media),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      navDrawerLogout(media),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget navDrawerHistory(Size media) {
    return NavMenu(
      onTap: () {
        historyFiltter = '';
        myHistory.clear();
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: const History(),
          withNavBar: false,
        );
      },
      text: languages[choosenLanguage]['text_my_orders'],
      image: AppAssets.history,
    );
  }

  Widget navDrawerOrders(Size media) {
    return NavMenu(
      onTap: () {
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: const OrdersView(),
          withNavBar: false,
        );
      },
      text: 'طلباتي',
      icon: Icons.shopping_cart,
    );
  }

  Widget navDrawerNotification(Size media) {
    return ValueListenableBuilder(
        valueListenable: valueNotifierNotification.value,
        builder: (context, value, child) {
          return InkWell(
            onTap: () {
              PersistentNavBarNavigator.pushNewScreen(
                context,
                screen: const NotificationPage(),
                withNavBar: false,
              );
              setState(() => userDetails['notifications_count'] = 0);
            },
            child: Container(
              padding: EdgeInsets.only(top: media.width * 0.05),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.notifications_none,
                        size: media.width * 0.065,
                        color: textColor.withOpacity(0.5),
                      ),
                      SizedBox(width: media.width * 0.025),
                      ShowUp(
                        delay: 50,
                        child: MyText(
                          text: languages[choosenLanguage]['text_notification']
                              .toString(),
                          overflow: TextOverflow.ellipsis,
                          size: media.width * sixteen,
                          color: textColor.withOpacity(0.8),
                        ),
                      ),
                      const Spacer(),
                      if (userDetails['notifications_count'] != 0)
                        Container(
                          padding: const EdgeInsets.all(6),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: buttonColor,
                          ),
                          child: Text(
                            userDetails['notifications_count'].toString(),
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: buttonText,
                            ),
                          ),
                        ),
                    ],
                  ),
                  Container(
                    alignment: Alignment.centerRight,
                    padding: EdgeInsets.only(
                      top: media.width * 0.05,
                      left: media.width * 0.09,
                    ),
                    child: Container(
                      color: textColor.withOpacity(0.1),
                      height: 1,
                    ),
                  )
                ],
              ),
            ),
          );
        });
  }

  Widget navDrawerSOS(Size media) {
    return NavMenu(
      onTap: () async {
        var nav = await PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: const Sos(),
          withNavBar: false,
        );
        if (nav) {
          setState(() {});
        }
      },
      text: languages[choosenLanguage]['text_sos'],
      icon: Icons.connect_without_contact,
    );
  }

  Widget navDrawerMakeComplaints(Size media) {
    return NavMenu(
      icon: Icons.toc,
      text: languages[choosenLanguage]['text_make_complaints'],
      onTap: () {
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: const MakeComplaint(),
          withNavBar: false,
        );
      },
    );
  }

  Widget navDrawerFavorite(Size media) {
    return NavMenu(
      onTap: () {
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: const Favorite(),
          withNavBar: false,
        );
      },
      text: languages[choosenLanguage]['text_favourites'],
      icon: Icons.bookmark,
    );
  }

  Widget navDrawerSupport(Size media) {
    return ValueListenableBuilder(
      valueListenable: valueNotifierChat.value,
      builder: (context, value, child) {
        return InkWell(
          onTap: () {
            PersistentNavBarNavigator.pushNewScreen(
              context,
              screen: const SupportPage(),
              withNavBar: false,
            );
          },
          child: Container(
            padding: EdgeInsets.only(top: media.width * 0.05),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.support_agent,
                      size: media.width * 0.065,
                      color: textColor.withOpacity(0.5),
                    ),
                    SizedBox(width: media.width * 0.025),
                    SizedBox(
                      child: ShowUp(
                        delay: 50,
                        child: MyText(
                          text: languages[choosenLanguage]['text_support'],
                          overflow: TextOverflow.ellipsis,
                          size: media.width * sixteen,
                          color: textColor.withOpacity(0.8),
                        ),
                      ),
                    ),
                    const Spacer(),
                    if ((unSeenChatCount != '0'))
                      Container(
                        padding: const EdgeInsets.all(6),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: buttonColor,
                        ),
                        child: Text(
                          unSeenChatCount,
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: buttonText,
                          ),
                        ),
                      ),
                  ],
                ),
                Container(
                  alignment: Alignment.centerRight,
                  padding: EdgeInsets.only(
                    top: media.width * 0.05,
                    left: media.width * 0.09,
                  ),
                  child: Container(
                    color: textColor.withOpacity(0.1),
                    height: 1,
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  Widget navDrawerReferral(Size media) {
    return NavMenu(
      onTap: () {
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: const ReferralPage(),
          withNavBar: false,
        );
      },
      text: languages[choosenLanguage]['text_enable_referal'],
      image: AppAssets.referralIcon,
    );
  }

  Widget navDrawerLogout(Size media) {
    return InkWell(
      onTap: () {
        // setState(() => logout = true);
        valueNotifierHome.incrementNotifier();
        BlocProvider.of<BottomNavBarCubit>(context).changeVisibility(false);
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LoginView()),
          (route) => false,
        );
      },
      child: Container(
        padding: EdgeInsets.only(
          left: media.width * 0.25,
        ),
        height: media.width * 0.13,
        width: media.width * 0.8,
        color: Colors.red.withOpacity(0.2),
        child: Row(
          mainAxisAlignment: (languageDirection == 'ltr')
              ? MainAxisAlignment.start
              : MainAxisAlignment.end,
          children: [
            Icon(
              Icons.logout,
              size: media.width * 0.05,
              color: Colors.red,
            ),
            SizedBox(width: media.width * 0.025),
            MyText(
              text: languages[choosenLanguage]['text_sign_out'],
              size: media.width * sixteen,
              color: Colors.red,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            )
          ],
        ),
      ),
    );
  }
}
