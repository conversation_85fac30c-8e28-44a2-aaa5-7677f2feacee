import 'package:flutter/foundation.dart';
import 'package:safir_client/functions/functions.dart';
import 'package:http/http.dart' as http;

class Api {
  static const String baserUrl = 'https://shaklabx.itechnologyeg.com/';

  Future<dynamic> get({
    required String url,
    bool isAuthorized = false,
    bool isContentType = false,
  }) async {
    Map<String, String> headers = {};
    if (choosenLanguage.isNotEmpty) {
      headers.addAll({'X-localization': choosenLanguage});
    } else {
      headers.addAll({'X-localization': 'en'});
    }
    if (isContentType) {
      headers.addAll({'Content-Type': 'application/json'});
    }
    if (isAuthorized) {
      headers.addAll({'Authorization': 'Bearer ${bearerToken[0].token}'});
    }
    debugPrint('=========================================================');
    debugPrint('Api.get info: url = ${baserUrl + url},\n headers $headers');
    debugPrint('=========================================================');
    http.Response response = await http.get(
      Uri.parse(baserUrl + url),
      headers: headers,
    );
    debugPrint('get request end');

    return response;
  }

  Future<dynamic> post({
    required String url,
    dynamic body,
    bool isAuthorized = false,
    bool isContentType = false,
  }) async {
    Map<String, String> headers = {};
    if (choosenLanguage.isNotEmpty) {
      headers.addAll({'X-localization': choosenLanguage});
    } else {
      headers.addAll({'X-localization': 'ar'});
    }
    if (isContentType) {
      headers.addAll({'Content-Type': 'application/json'});
    }
    if (isAuthorized) {
      headers.addAll({'Authorization': 'Bearer ${bearerToken[0].token}'});
    }
    debugPrint('=========================================================');
    debugPrint('Api.post info: url = ${baserUrl + url},\n headers $headers');
    debugPrint('=========================================================');
    http.Response response = await http.post(
      Uri.parse(baserUrl + url),
      body: body,
      headers: headers,
    );
    debugPrint('post request end');
    return response;
  }
}
