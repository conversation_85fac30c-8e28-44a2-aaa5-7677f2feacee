import 'package:animated_splash_screen/animated_splash_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:safir_client/Core/services/service_locator.dart';
import 'package:safir_client/Core/utils/app_assets.dart';
import 'package:safir_client/pages/Navigation%20Bar/manager/bottom_nav_bar_cubit/bottom_nav_bar_cubit.dart';
import 'package:safir_client/pages/Navigation%20Bar/manager/language_cubit/language_cubit.dart';
import 'functions/functions.dart';
import 'functions/notifications.dart';
import 'pages/loadingPage/loading_data_view.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'package:flutter/services.dart';
import 'package:page_transition/page_transition.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setPreferredOrientations(
    [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown],
  );
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  setupServiceLocator();
  checkInternetConnection();
  initMessaging();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    platform = Theme.of(context).platform;
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => LanguageCubit()),
        BlocProvider(create: (context) => BottomNavBarCubit()),
      ],
      child: BlocBuilder<LanguageCubit, LanguageState>(
        builder: (context, state) {
          return GestureDetector(
            onTap: () {
              //remove keyboard on touching anywhere on the screen.
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
                FocusManager.instance.primaryFocus?.unfocus();
              }
            },
            child: ValueListenableBuilder(
              valueListenable: valueNotifierBook.value,
              builder: (context, value, child) {
                return MaterialApp(
                  debugShowCheckedModeBanner: false,
                  title: 'ShaklabX',
                  theme: ThemeData(
                    fontFamily: 'Cairo',
                    colorScheme: const ColorScheme.light(
                      primary: Color(0xff2778A0),
                    ),
                  ),
                  home: AnimatedSplashScreen(
                    duration: 1200,
                    splash: Image.asset(AppAssets.logoWithName),
                    nextScreen: const LoadingDataView(),
                    splashTransition: SplashTransition.slideTransition,
                    pageTransitionType: PageTransitionType.fade,
                    centered: true,
                    splashIconSize: 500.0,
                    customTween: Tween<Offset>(
                      begin: const Offset(-1, 0.0),
                      end: Offset.zero,
                    ),
                    curve: Curves.easeOut,
                    animationDuration: const Duration(milliseconds: 1000),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
